package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("TransactionResult Tests")
class TransactionResultTest {

    private TransactionContext testContext;
    private Exception testException;

    @BeforeEach
    void setUp() {
        testContext = new TransactionContext();
        testContext.put("test_key", "test_value");
        testContext.addExecutedStep("step1");

        testException = new IllegalArgumentException("Test exception message");
    }

    @Nested
    @DisplayName("Factory Methods")
    class FactoryMethodsTest {

        @Test
        @DisplayName("success() should create successful TransactionResult")
        void success_shouldCreateSuccessfulResult() {
            // Act
            TransactionResult result = TransactionResult.success(testContext);

            // Assert
            assertTrue(result.success(), "Result should be successful");
            assertNull(result.exception(), "Exception should be null for successful result");
            assertSame(testContext, result.context(), "Context should be the same instance");
        }

        @Test
        @DisplayName("success() should handle null context")
        void success_shouldHandleNullContext() {
            // Act
            TransactionResult result = TransactionResult.success(null);

            // Assert
            assertTrue(result.success(), "Result should be successful");
            assertNull(result.exception(), "Exception should be null");
            assertNull(result.context(), "Context should be null");
        }

        @Test
        @DisplayName("failure() should create failed TransactionResult")
        void failure_shouldCreateFailedResult() {
            // Act
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNotNull(result.exception(), "Exception should not be null");
            assertSame(testContext, result.context(), "Context should be the same instance");
        }

        @Test
        @DisplayName("failure() should handle null exception")
        void failure_shouldHandleNullException() {
            // Act
            TransactionResult result = TransactionResult.failure(null, testContext);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNull(result.exception(), "Exception should be null when input is null");
            assertSame(testContext, result.context(), "Context should be the same instance");
        }

        @Test
        @DisplayName("failure() should handle null context")
        void failure_shouldHandleNullContext() {
            // Act
            TransactionResult result = TransactionResult.failure(testException, null);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNotNull(result.exception(), "Exception should not be null");
            assertNull(result.context(), "Context should be null");
        }

        @Test
        @DisplayName("failure() should handle both null exception and context")
        void failure_shouldHandleBothNullValues() {
            // Act
            TransactionResult result = TransactionResult.failure(null, null);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNull(result.exception(), "Exception should be null");
            assertNull(result.context(), "Context should be null");
        }
    }

    @Nested
    @DisplayName("Exception Handling")
    class ExceptionHandlingTest {

        @Test
        @DisplayName("exception() should return raw exception (record accessor)")
        void exception_shouldReturnRawException() {
            // Arrange
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Act
            Exception returnedException = result.exception();

            // Assert
            assertNotNull(returnedException, "Returned exception should not be null");
            assertSame(
                    testException,
                    returnedException,
                    "Record accessor should return the same instance");
        }

        @Test
        @DisplayName("exception() should return null when original exception is null")
        void exception_shouldReturnNullWhenOriginalIsNull() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            Exception returnedException = result.exception();

            // Assert
            assertNull(returnedException, "Should return null when original exception is null");
        }

        @Test
        @DisplayName("getException() should return defensive copy wrapped in RuntimeException")
        void getException_shouldReturnDefensiveCopy() {
            // Arrange
            TransactionResult failureResult = TransactionResult.failure(testException, testContext);
            TransactionResult successResult = TransactionResult.success(testContext);

            // Act & Assert for failure result
            Exception rawException = failureResult.exception();
            Exception defensiveCopy = failureResult.getException();

            // Raw exception should be the original
            assertSame(testException, rawException);

            // Defensive copy should be wrapped RuntimeException
            assertTrue(defensiveCopy instanceof RuntimeException);
            assertEquals(testException.getMessage(), defensiveCopy.getMessage());
            assertSame(testException, defensiveCopy.getCause());
            assertNotSame(testException, defensiveCopy);

            // Act & Assert for success result
            assertNull(successResult.exception());
            assertNull(successResult.getException());
        }

        @Test
        @DisplayName("getException() should preserve exception type information in message")
        void getException_shouldPreserveExceptionTypeInformation() {
            // Arrange
            Exception specificException = new IllegalStateException("Specific error message");
            TransactionResult result = TransactionResult.failure(specificException, testContext);

            // Act
            Exception returnedException = result.getException();

            // Assert
            assertEquals("Specific error message", returnedException.getMessage());
            assertTrue(returnedException.getCause() instanceof IllegalStateException);
            assertTrue(returnedException instanceof RuntimeException);
        }
    }

    @Nested
    @DisplayName("Context Handling")
    class ContextHandlingTest {

        @Test
        @DisplayName("context() should return the same TransactionContext instance")
        void context_shouldReturnSameInstance() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            TransactionContext returnedContext = result.context();

            // Assert
            assertSame(testContext, returnedContext, "Should return the same context instance");
        }

        @Test
        @DisplayName("context() should preserve context data")
        void context_shouldPreserveContextData() {
            // Arrange
            testContext.put("additional_key", "additional_value");
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Act
            TransactionContext returnedContext = result.context();

            // Assert
            assertEquals("test_value", returnedContext.get("test_key"));
            assertEquals("additional_value", returnedContext.get("additional_key"));
            assertEquals(1, returnedContext.getExecutedSteps().size());
            assertEquals("step1", returnedContext.getExecutedSteps().get(0));
        }
    }

    @Nested
    @DisplayName("Record Properties")
    class RecordPropertiesTest {

        @Test
        @DisplayName("success property should reflect the success state")
        void success_shouldReflectSuccessState() {
            // Arrange & Act
            TransactionResult successResult = TransactionResult.success(testContext);
            TransactionResult failureResult = TransactionResult.failure(testException, testContext);

            // Assert
            assertTrue(successResult.success());
            assertFalse(failureResult.success());
        }

        @Test
        @DisplayName("Record should implement equals and hashCode correctly")
        void record_shouldImplementEqualsAndHashCodeCorrectly() {
            // Arrange
            TransactionResult result1 = TransactionResult.success(testContext);
            TransactionResult result2 = TransactionResult.success(testContext);
            TransactionResult result3 = TransactionResult.failure(testException, testContext);

            // Assert
            assertEquals(result1, result2, "Results with same values should be equal");
            assertEquals(result1.hashCode(), result2.hashCode(), "Hash codes should be equal");
            assertNotEquals(result1, result3, "Results with different values should not be equal");
        }

        @Test
        @DisplayName("Record should implement toString correctly")
        void record_shouldImplementToStringCorrectly() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            String toString = result.toString();

            // Assert
            assertNotNull(toString);
            assertTrue(toString.contains("TransactionResult"));
            assertTrue(toString.contains("success=true"));
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    class EdgeCasesTest {

        @Test
        @DisplayName("Should handle RuntimeException as original exception")
        void shouldHandleRuntimeExceptionAsOriginal() {
            // Arrange
            RuntimeException originalRuntimeException =
                    new RuntimeException("Original runtime exception");
            TransactionResult result =
                    TransactionResult.failure(originalRuntimeException, testContext);

            // Act - Test both accessors
            Exception rawException = result.exception();
            Exception defensiveCopy = result.getException();

            // Assert - Raw exception should be the original
            assertSame(originalRuntimeException, rawException);

            // Assert - Defensive copy should be wrapped
            assertTrue(defensiveCopy instanceof RuntimeException);
            assertEquals("Original runtime exception", defensiveCopy.getMessage());
            assertSame(originalRuntimeException, defensiveCopy.getCause());
        }

        @Test
        @DisplayName("Should handle checked exceptions")
        void shouldHandleCheckedExceptions() {
            // Arrange
            Exception checkedException = new Exception("Checked exception message");
            TransactionResult result = TransactionResult.failure(checkedException, testContext);

            // Act - Test both accessors
            Exception rawException = result.exception();
            Exception defensiveCopy = result.getException();

            // Assert - Raw exception should be the original
            assertSame(checkedException, rawException);
            assertTrue(rawException instanceof Exception);

            // Assert - Defensive copy should be wrapped in RuntimeException
            assertTrue(defensiveCopy instanceof RuntimeException);
            assertEquals("Checked exception message", defensiveCopy.getMessage());
            assertSame(checkedException, defensiveCopy.getCause());
        }
    }
}
