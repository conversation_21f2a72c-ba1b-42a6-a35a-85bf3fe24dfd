package com.sbg.ug.optimus_backend.billing.orchastration;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class TransactionResultTest {

    @Test
    void success_shouldCreateSuccessfulResult() {
        // Arrange
        TransactionContext context = new TransactionContext();
        context.put("test_key", "test_value");

        // Act
        TransactionResult result = TransactionResult.success(context);

        // Assert
        assertTrue(result.isSuccess());
        assertNull(result.getException());
        assertNotNull(result.getContext());
        assertEquals("test_value", result.getContext().get("test_key"));
    }

    @Test
    void failure_shouldCreateFailureResult() {
        // Arrange
        TransactionContext context = new TransactionContext();
        context.put("error_key", "error_value");
        Exception testException = new RuntimeException("Test failure");

        // Act
        TransactionResult result = TransactionResult.failure(testException, context);

        // Assert
        assertFalse(result.isSuccess());
        assertNotNull(result.getException());
        // The getException() method returns a defensive copy wrapped in RuntimeException
        assertTrue(result.getException() instanceof RuntimeException);
        assertEquals("Test failure", result.getException().getMessage());
        assertEquals(testException, result.getException().getCause());
        assertNotNull(result.getContext());
        assertEquals("error_value", result.getContext().get("error_key"));
    }

    @Test
    void success_withNullContext_shouldHandleGracefully() {
        // Act
        TransactionResult result = TransactionResult.success(null);

        // Assert
        assertTrue(result.isSuccess());
        assertNull(result.getException());
        assertNull(result.getContext());
    }

    @Test
    void failure_withNullContext_shouldHandleGracefully() {
        // Arrange
        Exception testException = new IllegalArgumentException("Null context test");

        // Act
        TransactionResult result = TransactionResult.failure(testException, null);

        // Assert
        assertFalse(result.isSuccess());
        // The getException() method returns a defensive copy wrapped in RuntimeException
        assertTrue(result.getException() instanceof RuntimeException);
        assertEquals("Null context test", result.getException().getMessage());
        assertEquals(testException, result.getException().getCause());
        assertNull(result.getContext());
    }

    @Test
    void failure_withNullException_shouldHandleGracefully() {
        // Arrange
        TransactionContext context = new TransactionContext();

        // Act
        TransactionResult result = TransactionResult.failure(null, context);

        // Assert
        assertFalse(result.isSuccess());
        assertNull(result.getException());
        assertNotNull(result.getContext());
    }

    @Test
    void getters_shouldReturnCorrectValues() {
        // Arrange
        TransactionContext context = new TransactionContext();
        context.put("getter_test", "value");
        RuntimeException exception = new RuntimeException("Getter test exception");

        // Test success result getters
        TransactionResult successResult = TransactionResult.success(context);
        assertTrue(successResult.isSuccess());
        assertNull(successResult.getException());
        assertSame(context, successResult.getContext());

        // Test failure result getters
        TransactionResult failureResult = TransactionResult.failure(exception, context);
        assertFalse(failureResult.isSuccess());
        // The getException() method returns a defensive copy wrapped in RuntimeException
        assertTrue(failureResult.getException() instanceof RuntimeException);
        assertEquals("Getter test exception", failureResult.getException().getMessage());
        assertEquals(exception, failureResult.getException().getCause());
        assertSame(context, failureResult.getContext());
    }

    @Test
    void getException_withNullException_shouldReturnNull() {
        // Arrange
        TransactionContext context = new TransactionContext();

        // Act - Create a success result (which has null exception internally)
        TransactionResult result = TransactionResult.success(context);

        // Assert - This tests the null branch in getException()
        assertNull(result.getException());
    }
}
