package com.sbg.ug.optimus_backend.billing.orchastration;

public record TransactionResult(boolean success, Exception exception, TransactionContext context) {

    // Custom accessor that returns a defensive copy to prevent exposure of internal representation
    @Override
    public Exception exception() {
        if (exception == null) {
            return null;
        }
        return new RuntimeException(exception.getMessage(), exception);
    }

    // Convenience method for checking success state
    public boolean isSuccess() {
        return success;
    }

    // Factory methods
    public static TransactionResult success(TransactionContext context) {
        return new TransactionResult(true, null, context);
    }

    public static TransactionResult failure(Exception exception, TransactionContext context) {
        return new TransactionResult(false, exception, context);
    }
}
