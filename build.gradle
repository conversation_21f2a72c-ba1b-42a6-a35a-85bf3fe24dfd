

plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.3'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'org.springframework.cloud.contract' version '4.2.0'
	id 'com.diffplug.spotless' version "7.0.2"
	id 'jacoco'
	id 'com.github.spotbugs' version "6.1.7"
	id 'com.github.jakemarsden.git-hooks' version '0.0.2'
	id 'pmd'
	id 'org.barfuin.gradle.jacocolog' version '3.1.0'
}


group = 'com.sbg.ug'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}


configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
	mavenLocal()
}

ext {
	set('springCloudVersion', "2024.0.0")
	set('springModulithVersion', "1.3.2")
}

spotbugs {
	toolVersion = '4.9.2'
	excludeFilter = file('config/spotbugs/exclude-filter.xml')
}


spotbugsMain {
	classes = fileTree('build/classes/java/main')
	projectName= 'Project Optimus'
	showProgress = true
	showStackTraces = true
	effort = com.github.spotbugs.snom.Effort.MAX
//	reportLevel= com.github.spotbugs.snom.Confidence.DEFAULT
}
spotbugsTest {
	classes = fileTree('build/classes/java/test')
	projectName= 'Project Optimus'
	showProgress = true
	showStackTraces = true
	effort = com.github.spotbugs.snom.Effort.MAX
//	reportLevel= com.github.spotbugs.snom.Confidence.HIGH
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.18.3'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.liquibase:liquibase-core'
	runtimeOnly 'org.postgresql:postgresql'

	implementation 'org.springframework.modulith:spring-modulith-starter-core'
	implementation 'org.projectlombok:lombok:1.18.36'
	implementation 'com.auth0:java-jwt:4.4.0'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'

	compileOnly 'org.projectlombok:lombok'
	testCompileOnly 'org.projectlombok:lombok'

	runtimeOnly 'org.springframework.modulith:spring-modulith-actuator'
	runtimeOnly 'org.springframework.modulith:spring-modulith-observability'


//	implementation 'com.sbg.domain.security:domain-service-security-spring-boot:7.4.2'

	implementation 'com.konghq:unirest-java-core:4.4.5'
	implementation 'com.konghq:unirest-modules-jackson:4.4.5'
	implementation 'com.konghq:unirest-modules-mocks:4.4.5'
	//implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.0'

	implementation 'org.mapstruct:mapstruct:1.6.3'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'


	annotationProcessor 'org.projectlombok:lombok'
	testAnnotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.cloud:spring-cloud-starter-contract-verifier'
	testImplementation 'org.springframework.modulith:spring-modulith-starter-test'
	testImplementation 'org.assertj:assertj-core:3.27.3'
	testImplementation 'org.instancio:instancio-core:5.4.1'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.modulith:spring-modulith-bom:${springModulithVersion}"
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}


contracts {

	baseClassMappings {
		baseClassForTests = 'com.sbg.ug.optimus_backend.BaseContract'
	}
}


spotless {

	format 'misc', {
		// define the files to apply `misc` to
		target '*.gradle', '.gitattributes', '.gitignore'

		// define the steps to apply to those files
		trimTrailingWhitespace()
		leadingSpacesToTabs() // or leadingTabsToSpaces. Takes an integer argument if you don't like 4
		endWithNewline()
	}
	java {
		// don't need to set target, it is inferred from java

		// apply a specific flavor of google-java-format
		googleJavaFormat('1.25.2').aosp().reflowLongStrings().skipJavadocFormatting()
		// fix formatting of type annotations
		formatAnnotations()

		targetExclude '**/restcontracts/*.java' // Exclude contract test classes from formatting

	}
}

test {
	finalizedBy jacocoTestReport // report is always generated after tests run
}

contractTest {
	finalizedBy jacocoTestReport, jacocoTestCoverageVerification // report is always generated after tests run
}


def jacocoIgnored = ['**/*MapperImpl.class',
					'**/*Config.class',
					"**/v1/data/*Request*.class",
					"**/http/data/*Dto*.class",
					"**/exceptions/**/*ErrorCode*.class",
					"**/spikes/**"]

jacocoTestReport {
	dependsOn test // tests are required to run before generating the report
	executionData = files('build/jacoco/test.exec', 'build/jacoco/contractTest.exec')

	classDirectories.setFrom(files(classDirectories.files.collect {
		fileTree(dir: it, excludes: jacocoIgnored)
	}))

	reports {
		xml.required = false
		csv.required = false
	}
}

jacocoTestCoverageVerification {

	def ignored = ['com.sbg.ug.optimus_backend.OptimusBackendApplication',
				'com.sbg.ug.optimus_backend.billing.SpikeController',
				'com.sbg.ug.optimus_backend.config.WebMvcConfig',
				'com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd']

	classDirectories.setFrom(files(classDirectories.files.collect {
		fileTree(dir: it, excludes: jacocoIgnored)
	}))

	executionData = files('build/jacoco/test.exec', 'build/jacoco/contractTest.exec')


	violationRules {


		rule {
			enabled = false //disable till we have enough code
			excludes = ignored
			limit {
				minimum = 0.8
			}
		}


		//below ar not hard rules but rather helpful hints on how to improve the code quality
		//lines in a class
		rule {
			enabled = true
			element = 'CLASS'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'LINE'
				value = 'TOTALCOUNT'
				maximum = 500
			}
		}

		//lines in a method
		rule {
			enabled = true
			element = 'METHOD'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'LINE'
				value = 'TOTALCOUNT'
				maximum = 50
			}
		}
		rule {
			enabled = true
			element = 'CLASS'
			includes = ['com.sbg.ug.optimus_backend.*'] // Adjust package as needed
			excludes = ['com.sbg.ug.optimus_backend.OptimusBackendApplication']
			excludes = ignored

			limit {
				counter = 'INSTRUCTION'
				value = 'COVEREDRATIO'
				minimum = 0.80 // At least 80% instruction coverage
			}
		}

		rule {
			enabled = true
			element = 'CLASS'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'BRANCH'
				value = 'COVEREDRATIO'
				minimum = 0.75 // At least 75% branch coverage
			}
		}

		rule {
			enabled = true
			element = 'METHOD'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'LINE'
				value = 'TOTALCOUNT'
				maximum = 50 // Max 50 lines per method
			}
		}

		rule {
			enabled = true
			element = 'METHOD'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'COMPLEXITY'
				value = 'TOTALCOUNT'
				maximum = 10 // Limit cyclomatic complexity per method
			}
		}

		rule {
			enabled = true
			element = 'CLASS'
			includes = ['com.sbg.ug.optimus_backend.*']
			excludes = ignored

			limit {
				counter = 'METHOD'
				value = 'COVEREDRATIO'
				minimum = 0.80 // Ensure at least 80% of methods are covered
			}
		}
	}


}

tasks.named('contractTest') {
	useJUnitPlatform()
}

tasks.named('test') {
	useJUnitPlatform()
}


tasks.named('spotlessJava') {
	it.dependsOn('generateContractTests')
}

tasks.named('spotbugsMain') {
	it.dependsOn('compileJava')
}

tasks.named('spotbugsTest') {
	it.dependsOn('compileTestJava')
}

tasks.spotbugsMain {
	reports.create("html") {
		required = true
		outputLocation = file("${layout.buildDirectory.get()}/reports/spotbugs/spotbugs-main.html")
		setStylesheet("fancy-hist.xsl")
	}
}

tasks.spotbugsTest {
	reports.create("html") {
		required = true
		outputLocation = file("${layout.buildDirectory.get()}/reports/spotbugs/spotbugs-test.html")
		setStylesheet("fancy-hist.xsl")
	}
}


gitHooks {
	hooks = [
			'pre-push': 'check'
	]
}

pmd {
	consoleOutput = true
	toolVersion = "7.11.0"
	rulesMinimumPriority = 5
	ruleSetFiles = files("config/pmd/rulesets.xml")
}
